const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { exec, spawn } = require('child_process');
const cors = require('cors');
const FormData = require('form-data');
const axios = require('axios');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.static('.'));
app.use(express.json());

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = './uploads';
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir);
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 100 * 1024 * 1024 // 100MB limit
    },
    fileFilter: (req, file, cb) => {
        if (path.extname(file.originalname).toLowerCase() === '.rvm') {
            cb(null, true);
        } else {
            cb(new Error('Only RVM files are allowed'));
        }
    }
});

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Upload and convert endpoint
app.post('/convert', upload.single('rvmFile'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        const inputPath = req.file.path;
        const outputPath = inputPath.replace('.rvm', '.glb');
        const originalName = req.file.originalname;
        const outputName = originalName.replace('.rvm', '.glb');

        console.log(`Converting ${originalName} to GLB...`);
        console.log(`Input path: ${inputPath}`);
        console.log(`Output path: ${outputPath}`);

        // Convert using rvmparser
        await convertWithRvmParser(inputPath, outputPath);

        // Check if conversion was successful
        if (!fs.existsSync(outputPath)) {
            throw new Error('Conversion failed - output file not created');
        }

        console.log(`Sending file: ${outputPath} as ${outputName}`);
        console.log(`File size: ${fs.statSync(outputPath).size} bytes`);

        // Set proper headers for file download
        res.setHeader('Content-Type', 'application/octet-stream');
        res.setHeader('Content-Disposition', `attachment; filename="${outputName}"`);
        res.setHeader('Content-Length', fs.statSync(outputPath).size);

        // Send the file
        const fileStream = fs.createReadStream(outputPath);
        fileStream.pipe(res);

        fileStream.on('end', () => {
            console.log('File sent successfully');

            // Clean up files after download
            setTimeout(() => {
                try {
                    if (fs.existsSync(inputPath)) {
                        fs.unlinkSync(inputPath);
                        console.log('Cleaned up input file');
                    }
                    if (fs.existsSync(outputPath)) {
                        fs.unlinkSync(outputPath);
                        console.log('Cleaned up output file');
                    }
                } catch (cleanupError) {
                    console.error('Cleanup error:', cleanupError);
                }
            }, 2000);
        });

        fileStream.on('error', (err) => {
            console.error('File stream error:', err);
            if (!res.headersSent) {
                res.status(500).json({ error: 'File transfer failed' });
            }
        });

    } catch (error) {
        console.error('Conversion error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Conversion function using rvmparser
async function convertWithRvmParser(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
        const rvmparserPath = path.join(__dirname, 'rvmparser.exe');

        // Command: rvmparser.exe --output-gltf=output.glb input.rvm
        const args = [`--output-gltf=${outputPath}`, inputPath];

        console.log(`Starting rvmparser conversion: ${rvmparserPath} ${args.join(' ')}`);

        const rvmProcess = spawn(rvmparserPath, args, {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        rvmProcess.stdout.on('data', (data) => {
            stdout += data.toString();
            console.log('rvmparser stdout:', data.toString());
        });

        rvmProcess.stderr.on('data', (data) => {
            stderr += data.toString();
            console.log('rvmparser stderr:', data.toString());
        });

        rvmProcess.on('close', (code) => {
            if (code === 0) {
                console.log('rvmparser conversion completed successfully');
                resolve({ success: true, stdout, stderr });
            } else {
                console.error('rvmparser conversion failed with code:', code);
                reject(new Error(`rvmparser process exited with code ${code}. Stderr: ${stderr}`));
            }
        });

        rvmProcess.on('error', (error) => {
            console.error('Failed to start rvmparser process:', error);
            reject(error);
        });
    });
}

// Conversion method using Aspose.3D
async function convertWithAsposeCloud(inputPath, outputPath) {
    return new Promise(async (resolve, reject) => {
        try {
            console.log('Starting RVM to GLB conversion...');

            // Method 1: Try using Aspose.3D Node.js library
            // try {
            //     console.log('Attempting Aspose.3D Node.js conversion...');
            //     await convertWithAsposeNodeJS(inputPath, outputPath);
            //     console.log('Aspose.3D Node.js conversion completed successfully');
            //     resolve();
            //     return;
            // } catch (asposeError) {
            //     console.log('Aspose.3D Node.js failed:', asposeError.message);
            // }

            // Method 2: Try using Python with Aspose.3D
            try {
                console.log('Attempting Python Aspose.3D conversion...');
                await convertWithAsposePython(inputPath, outputPath);
                console.log('Python Aspose.3D conversion completed successfully');
                resolve();
                return;
            } catch (pythonError) {
                console.log('Python Aspose.3D failed:', pythonError.message);
            }

            // Method 3: Try local conversion tools if available
            // try {
            //     console.log('Attempting local conversion tools...');
            //     await convertWithLocalTools(inputPath, outputPath);
            //     console.log('Local conversion completed successfully');
            //     resolve();
            //     return;
            // } catch (localError) {
            //     console.log('Local conversion failed:', localError.message);
            // }

            // Method 4: Fallback to template (temporary)
            // console.log('All conversion methods failed, using template as fallback...');
            // const demoGlbPath = './marine_rvm.glb';
            // if (fs.existsSync(demoGlbPath)) {
            //     fs.copyFileSync(demoGlbPath, outputPath);
            //     console.log('Using template GLB file as fallback');
            //     resolve();
            // } else {
            //     reject(new Error('All conversion methods failed and no template available'));
            // }

        } catch (error) {
            console.error('Conversion error:', error.message);
            reject(new Error(`Conversion failed: ${error.message}`));
        }
    });
}

// Method 1: Aspose.3D Cloud API conversion
async function convertWithAsposeNodeJS(inputPath, outputPath) {
    return new Promise(async (resolve, reject) => {
        try {
            console.log('Using Aspose.3D Cloud API for conversion...');

            // Note: This requires Aspose Cloud API credentials
            // For now, we'll implement a basic version that can be extended

            // Read the input file
            const fileBuffer = fs.readFileSync(inputPath);
            const formData = new FormData();
            formData.append('file', fileBuffer, {
                filename: path.basename(inputPath),
                contentType: 'application/octet-stream'
            });

            // Try to use a conversion service (this is a placeholder)
            // In a real implementation, you would use proper Aspose Cloud API endpoints
            console.log('Attempting cloud-based conversion...');

            // For now, throw an error to move to the next method
            throw new Error('Aspose Cloud API credentials not configured');

        } catch (error) {
            console.error('Aspose Cloud API error:', error.message);
            reject(new Error(`Aspose Cloud API failed: ${error.message}`));
        }
    });
}

// Method 2: Aspose.3D Python conversion using virtual environment
async function convertWithAsposePython(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
        console.log('Using Aspose.3D Python for conversion...');

        // Create Python script for conversion using the exact syntax you provided
        const pythonScript = `
import sys
import os
import aspose.threed as a3d

try:
    print("Starting Aspose.3D conversion (trial version)")

    print("Loading RVM file: ${inputPath.replace(/\\/g, '/')}")
    scene = a3d.Scene.from_file("${inputPath.replace(/\\/g, '/')}")

    print("Saving as GLB: ${outputPath.replace(/\\/g, '/')}")
    scene.save("${outputPath.replace(/\\/g, '/')}")

    print("Conversion completed successfully")
    print("Note: Using Aspose.3D trial version - consider purchasing a license for production use")

except Exception as e:
    error_msg = str(e)
    if "TrialException" in error_msg:
        print("Trial license limitation encountered")
        print("The conversion may still work with trial watermarks/limitations")
        # Check if output file was created despite the trial exception
        if os.path.exists("${outputPath.replace(/\\/g, '/')}"):
            print("Output file was created successfully (with trial limitations)")
        else:
            print("Output file was not created due to trial limitations")
            sys.exit(1)
    else:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
`;

        // Write Python script to temporary file
        const scriptPath = 'temp_convert.py';
        fs.writeFileSync(scriptPath, pythonScript);

        // Use the virtual environment Python executable
        const pythonCommand = 'venv\\Scripts\\python.exe';
        const command = `"${pythonCommand}" "${scriptPath}"`;

        console.log('Executing Python conversion script with virtual environment...');
        console.log(`Command: ${command}`);

        exec(command, { cwd: process.cwd() }, (error, stdout, stderr) => {
            // Clean up script file
            try {
                fs.unlinkSync(scriptPath);
            } catch (cleanupError) {
                console.warn('Could not clean up Python script:', cleanupError.message);
            }

            if (error) {
                console.error('Python conversion error:', error.message);
                console.error('Python stderr:', stderr);
                console.error('Python stdout:', stdout);
                reject(new Error(`Python conversion failed: ${error.message}`));
                return;
            }

            console.log('Python stdout:', stdout);
            if (stderr) {
                console.log('Python stderr:', stderr);
            }

            // Check if the output file was created
            if (fs.existsSync(outputPath)) {
                console.log('Python Aspose.3D conversion successful');
                resolve();
            } else {
                reject(new Error('Python conversion failed - no output file created'));
            }
        });
    });
}

// Method 3: Fallback - Try local conversion tools
async function convertWithLocalTools(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
        // Try to use PmuTranslator if available
        const command = `PmuTranslator -f "${inputPath}" -o "${outputPath}"`;

        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.error('PmuTranslator not available:', error.message);
                reject(new Error('No conversion tools available'));
                return;
            }

            // Check if the output file was created
            if (fs.existsSync(outputPath)) {
                console.log('Local conversion completed successfully');
                resolve();
            } else {
                reject(new Error('Local conversion failed - no output file created'));
            }
        });
    });
}

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling middleware
app.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({ error: 'File too large. Maximum size is 100MB.' });
        }
    }

    console.error('Server error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
    console.log(`RVM to GLB Converter Server running on port ${PORT}`);
    console.log(`Open http://localhost:${PORT} in your browser`);
});

module.exports = app;
